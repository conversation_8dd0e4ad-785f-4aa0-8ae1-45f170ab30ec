import { useDroppable } from '@dnd-kit/core';
import { Card, Tag } from 'antd';
import React from 'react';
import { DragItem } from '../DragDropZones';
import DraggableItem from './DraggableItem';
interface SourceAreaProps {
  items: DragItem[];
}

// 源文件区域组件（上方）
const SourceArea: React.FC<SourceAreaProps> = ({ items }) => {
  const { isOver, setNodeRef } = useDroppable({
    id: 'source',
  });

  return (
    <div className={`source-area ${isOver ? 'source-over' : ''}`}>
      <Card
        title={
          <div className="area-header">
            <span className="area-header-title">
              以下文件无法匹配到对应车辆，请手动拖放到下方对应车辆位置
            </span>
            <Tag color="blue">{items.length}</Tag>
          </div>
        }
        size="small"
      >
        <div ref={setNodeRef} className="source-files-container">
          <div className="source-files-grid">
            {items.map((item) => (
              <DraggableItem key={item.id} item={item} isSource={true} />
            ))}
            {items.length === 0 && <div className="empty-source">拖拽文件到此区域</div>}
          </div>
        </div>
      </Card>
    </div>
  );
};

export default React.memo(SourceArea);
