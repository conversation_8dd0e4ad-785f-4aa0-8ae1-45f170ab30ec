import { useDroppable } from '@dnd-kit/core';
import { Card, Tag } from 'antd';
import React, { useMemo } from 'react';
import { DragItem } from '../DragDropZones';
import DraggableItem from './DraggableItem';

interface SourceAreaProps {
  items: DragItem[];
}

// 源文件区域组件（上方）- 优化性能版本
const SourceArea: React.FC<SourceAreaProps> = ({ items }) => {
  const { isOver, setNodeRef } = useDroppable({
    id: 'source',
  });

  // 使用useMemo优化渲染性能，避免每次都重新创建组件
  const renderedItems = useMemo(() => {
    if (items.length === 0) {
      return <div className="empty-source">拖拽文件到此区域</div>;
    }

    // 对于大量数据，只渲染前100个项目，其余的延迟渲染
    const visibleItems = items.slice(0, 100);
    const hasMore = items.length > 100;

    return (
      <>
        {visibleItems.map((item) => (
          <DraggableItem key={item.id} item={item} isSource={true} />
        ))}
        {hasMore && (
          <div className="more-items-indicator">
            还有 {items.length - 100} 个文件...
          </div>
        )}
      </>
    );
  }, [items]);

  // 使用useMemo优化CSS类名计算
  const containerClassName = useMemo(() => {
    return `source-area ${isOver ? 'source-over' : ''}`;
  }, [isOver]);

  return (
    <div className={containerClassName}>
      <Card
        title={
          <div className="area-header">
            <span className="area-header-title">
              以下文件无法匹配到对应车辆，请手动拖放到下方对应车辆位置
            </span>
            <Tag color="blue">{items.length}</Tag>
          </div>
        }
        size="small"
      >
        <div ref={setNodeRef} className="source-files-container">
          <div className="source-files-grid">
            {renderedItems}
          </div>
        </div>
      </Card>
    </div>
  );
};

export default React.memo(SourceArea);
