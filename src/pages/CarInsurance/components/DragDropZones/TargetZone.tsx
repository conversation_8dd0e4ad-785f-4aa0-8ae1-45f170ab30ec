import { useDroppable } from '@dnd-kit/core';
import { Card, Tag } from 'antd';
import React, { useMemo } from 'react';
import { DragItem } from '../DragDropZones';
import DraggableItem from './DraggableItem';

interface TargetZoneProps {
  id: string;
  title: string;
  items: DragItem[];
  onDeleteItem: (itemId: string) => void;
}

// 目标拖放区域组件（右侧）- 优化性能版本
const TargetZone: React.FC<TargetZoneProps> = ({ id, title, items, onDeleteItem }) => {
  const { isOver, setNodeRef } = useDroppable({
    id,
  });

  // 使用useMemo优化渲染性能
  const renderedContent = useMemo(() => {
    if (items.length === 0) {
      return <div className="empty-target">拖拽文件到此区域</div>;
    }

    // 对于大量数据，只渲染前50个项目
    const visibleItems = items.slice(0, 50);
    const hasMore = items.length > 50;

    return (
      <div className="target-files-list">
        {visibleItems.map((item) => (
          <DraggableItem key={item.id} item={item} onDelete={onDeleteItem} isSource={false} />
        ))}
        {hasMore && (
          <div className="more-items-indicator">
            还有 {items.length - 50} 个文件...
          </div>
        )}
      </div>
    );
  }, [items, onDeleteItem]);

  // 使用useMemo优化CSS类名计算
  const containerClassName = useMemo(() => {
    return `target-zone ${isOver ? 'target-over' : ''}`;
  }, [isOver]);

  return (
    <div className={containerClassName}>
      <Card
        title={
          <div className="zone-header">
            <span>{title}</span>
            <Tag color="green">{items.length}</Tag>
          </div>
        }
        size="small"
      >
        <div ref={setNodeRef} className="target-drop-area">
          {renderedContent}
        </div>
      </Card>
    </div>
  );
};

export default React.memo(TargetZone);
